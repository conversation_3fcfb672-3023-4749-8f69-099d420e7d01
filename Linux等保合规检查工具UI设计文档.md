# Linux远程等保合规检查工具 UI设计文档

## 项目概述

**技术栈**: Go Wails + React + Ant Design  
**目标**: 开发一个专业的Linux远程等保合规检查工具  
**设计原则**: 简洁、高效、专业、易用

## 整体架构设计

### 应用架构图

```mermaid
graph TB
    A[主应用窗口] --> B[顶部导航栏]
    A --> C[侧边栏导航]
    A --> D[主内容区域]
    A --> E[状态栏]
    
    B --> B1[工具标题]
    B --> B2[用户信息]
    B --> B3[系统设置]
    
    C --> C1[服务器管理]
    C --> C2[检查配置]
    C --> C3[执行检查]
    C --> C4[结果查看]
    C --> C5[报告管理]
    
    D --> D1[仪表板]
    D --> D2[服务器列表]
    D --> D3[检查项配置]
    D --> D4[检查执行]
    D --> D5[结果展示]
    D --> D6[报告生成]
```

### 页面层级结构

```mermaid
graph LR
    A[主界面] --> B[仪表板]
    A --> C[服务器管理]
    A --> D[检查配置]
    A --> E[执行检查]
    A --> F[结果查看]
    A --> G[报告管理]
    
    C --> C1[服务器列表]
    C --> C2[添加服务器]
    C --> C3[编辑服务器]
    
    D --> D1[检查项模板]
    D --> D2[自定义检查项]
    D --> D3[检查策略]
    
    E --> E1[选择目标]
    E --> E2[执行进度]
    E --> E3[实时日志]
    
    F --> F1[检查结果]
    F --> F2[问题详情]
    F --> F3[修复建议]
    
    G --> G1[报告列表]
    G --> G2[报告预览]
    G --> G3[报告导出]
```

## 主要页面布局设计

### 1. 主界面布局

**布局结构**: 经典的管理后台布局
- **顶部导航栏**: 高度 64px，包含标题、用户信息、设置按钮
- **侧边栏**: 宽度 240px，可折叠，包含主要功能导航
- **主内容区**: 自适应宽度，包含页面内容和面包屑导航
- **状态栏**: 高度 32px，显示连接状态、系统信息

```typescript
// 主布局组件结构
<Layout className="app-layout">
  <Header className="app-header">
    <div className="header-left">
      <h1>Linux等保合规检查工具</h1>
    </div>
    <div className="header-right">
      <Space>
        <Badge count={notifications}>
          <BellOutlined />
        </Badge>
        <Avatar src={userAvatar} />
        <Dropdown menu={userMenu}>
          <Button type="text">{userName}</Button>
        </Dropdown>
      </Space>
    </div>
  </Header>
  
  <Layout>
    <Sider collapsible collapsed={collapsed}>
      <Menu mode="inline" selectedKeys={selectedKeys}>
        <Menu.Item key="dashboard" icon={<DashboardOutlined />}>
          仪表板
        </Menu.Item>
        <Menu.Item key="servers" icon={<ServerOutlined />}>
          服务器管理
        </Menu.Item>
        <Menu.Item key="config" icon={<SettingOutlined />}>
          检查配置
        </Menu.Item>
        <Menu.Item key="execute" icon={<PlayCircleOutlined />}>
          执行检查
        </Menu.Item>
        <Menu.Item key="results" icon={<FileSearchOutlined />}>
          结果查看
        </Menu.Item>
        <Menu.Item key="reports" icon={<FileTextOutlined />}>
          报告管理
        </Menu.Item>
      </Menu>
    </Sider>
    
    <Layout className="content-layout">
      <Content className="main-content">
        <Breadcrumb className="breadcrumb">
          <Breadcrumb.Item>首页</Breadcrumb.Item>
          <Breadcrumb.Item>{currentPage}</Breadcrumb.Item>
        </Breadcrumb>
        <div className="page-content">
          {children}
        </div>
      </Content>
      
      <Footer className="status-bar">
        <Space>
          <span>连接状态: <Badge status="success" text="已连接" /></span>
          <span>当前用户: {currentUser}</span>
          <span>版本: v1.0.0</span>
        </Space>
      </Footer>
    </Layout>
  </Layout>
</Layout>
```

### 2. 仪表板页面

**功能**: 展示系统概览、统计信息、快速操作
**布局**: 卡片式布局，响应式设计

```typescript
// 仪表板组件
<div className="dashboard">
  <Row gutter={[16, 16]}>
    {/* 统计卡片 */}
    <Col xs={24} sm={12} md={6}>
      <Card>
        <Statistic
          title="服务器总数"
          value={serverCount}
          prefix={<ServerOutlined />}
        />
      </Card>
    </Col>
    <Col xs={24} sm={12} md={6}>
      <Card>
        <Statistic
          title="检查任务"
          value={taskCount}
          prefix={<CheckCircleOutlined />}
        />
      </Card>
    </Col>
    <Col xs={24} sm={12} md={6}>
      <Card>
        <Statistic
          title="风险项目"
          value={riskCount}
          prefix={<ExclamationCircleOutlined />}
          valueStyle={{ color: '#cf1322' }}
        />
      </Card>
    </Col>
    <Col xs={24} sm={12} md={6}>
      <Card>
        <Statistic
          title="合规率"
          value={complianceRate}
          suffix="%"
          prefix={<SafetyCertificateOutlined />}
          valueStyle={{ color: '#3f8600' }}
        />
      </Card>
    </Col>
  </Row>

  <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
    {/* 快速操作 */}
    <Col xs={24} lg={12}>
      <Card title="快速操作" extra={<Button type="link">更多</Button>}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button type="primary" block icon={<PlusOutlined />}>
            添加服务器
          </Button>
          <Button block icon={<PlayCircleOutlined />}>
            执行检查
          </Button>
          <Button block icon={<FileTextOutlined />}>
            生成报告
          </Button>
        </Space>
      </Card>
    </Col>

    {/* 最近活动 */}
    <Col xs={24} lg={12}>
      <Card title="最近活动">
        <List
          dataSource={recentActivities}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar icon={item.icon} />}
                title={item.title}
                description={item.description}
              />
              <div>{item.time}</div>
            </List.Item>
          )}
        />
      </Card>
    </Col>
  </Row>

  <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
    {/* 检查结果趋势图 */}
    <Col span={24}>
      <Card title="检查结果趋势">
        <div style={{ height: 300 }}>
          {/* 这里集成图表组件，如 ECharts 或 Chart.js */}
          <div className="chart-placeholder">
            趋势图表区域
          </div>
        </div>
      </Card>
    </Col>
  </Row>
</div>
```

### 3. 服务器管理页面

**功能**: 管理目标服务器，包括添加、编辑、删除、连接测试
**布局**: 表格 + 操作按钮

```typescript
// 服务器管理页面
<div className="server-management">
  <Card>
    <div className="page-header">
      <div className="header-title">
        <h2>服务器管理</h2>
        <p>管理需要进行等保合规检查的Linux服务器</p>
      </div>
      <div className="header-actions">
        <Space>
          <Button icon={<ReloadOutlined />} onClick={refreshServers}>
            刷新
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={showAddModal}>
            添加服务器
          </Button>
        </Space>
      </div>
    </div>

    <Table
      columns={serverColumns}
      dataSource={servers}
      rowKey="id"
      pagination={{
        total: serverTotal,
        pageSize: pageSize,
        current: currentPage,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条记录`,
      }}
      rowSelection={{
        selectedRowKeys: selectedServers,
        onChange: setSelectedServers,
      }}
    />
  </Card>
</div>
```

## 功能对话框设计

### 1. 服务器连接配置对话框

**用途**: 添加或编辑服务器连接信息
**尺寸**: 600px 宽度，自适应高度
**布局**: 表单布局，分组显示

```typescript
// 服务器配置对话框
<Modal
  title={editingServer ? "编辑服务器" : "添加服务器"}
  open={serverModalVisible}
  onOk={handleServerSubmit}
  onCancel={handleServerCancel}
  width={600}
  confirmLoading={submitting}
>
  <Form
    form={serverForm}
    layout="vertical"
    initialValues={editingServer}
  >
    <Divider orientation="left">基本信息</Divider>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          name="name"
          label="服务器名称"
          rules={[{ required: true, message: '请输入服务器名称' }]}
        >
          <Input placeholder="请输入服务器名称" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="host"
          label="IP地址/域名"
          rules={[{ required: true, message: '请输入IP地址或域名' }]}
        >
          <Input placeholder="*************" />
        </Form.Item>
      </Col>
    </Row>

    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          name="port"
          label="SSH端口"
          rules={[{ required: true, message: '请输入SSH端口' }]}
          initialValue={22}
        >
          <InputNumber min={1} max={65535} style={{ width: '100%' }} />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="os_version"
          label="操作系统版本"
        >
          <Input placeholder="CentOS 7.9" />
        </Form.Item>
      </Col>
    </Row>

    <Divider orientation="left">认证信息</Divider>
    <Form.Item
      name="auth_type"
      label="认证方式"
      rules={[{ required: true, message: '请选择认证方式' }]}
      initialValue="password"
    >
      <Radio.Group>
        <Radio value="password">密码认证</Radio>
        <Radio value="key">密钥认证</Radio>
      </Radio.Group>
    </Form.Item>

    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input placeholder="root" />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.auth_type !== currentValues.auth_type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('auth_type') === 'password' ? (
              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
            ) : (
              <Form.Item
                name="private_key"
                label="私钥文件"
                rules={[{ required: true, message: '请选择私钥文件' }]}
              >
                <Input placeholder="选择私钥文件路径" />
              </Form.Item>
            )
          }
        </Form.Item>
      </Col>
    </Row>

    <Divider orientation="left">高级设置</Divider>
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          name="timeout"
          label="连接超时(秒)"
          initialValue={30}
        >
          <InputNumber min={5} max={300} style={{ width: '100%' }} />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          name="sudo_password"
          label="Sudo密码"
        >
          <Input.Password placeholder="如需sudo权限请输入" />
        </Form.Item>
      </Col>
    </Row>

    <Form.Item name="description" label="描述">
      <Input.TextArea rows={3} placeholder="服务器描述信息" />
    </Form.Item>

    <div style={{ textAlign: 'center', marginTop: 16 }}>
      <Button
        type="dashed"
        icon={<ApiOutlined />}
        onClick={testConnection}
        loading={testing}
      >
        测试连接
      </Button>
    </div>
  </Form>
</Modal>
```

### 2. 检查项配置对话框

**用途**: 配置等保检查项目和规则
**尺寸**: 800px 宽度，自适应高度
**布局**: 分步表单，左侧分类树，右侧配置项

```typescript
// 检查项配置对话框
<Modal
  title="检查项配置"
  open={configModalVisible}
  onOk={handleConfigSubmit}
  onCancel={handleConfigCancel}
  width={800}
  style={{ top: 20 }}
>
  <Row gutter={16}>
    <Col span={8}>
      <Card title="检查分类" size="small">
        <Tree
          checkable
          checkedKeys={checkedCategories}
          onCheck={onCategoryCheck}
          treeData={checkCategories}
        />
      </Card>
    </Col>
    <Col span={16}>
      <Card title="检查项详情" size="small">
        <Form form={configForm} layout="vertical">
          <Form.Item name="check_name" label="检查项名称">
            <Input />
          </Form.Item>
          <Form.Item name="severity" label="严重级别">
            <Select>
              <Option value="high">高危</Option>
              <Option value="medium">中危</Option>
              <Option value="low">低危</Option>
              <Option value="info">信息</Option>
            </Select>
          </Form.Item>
          <Form.Item name="command" label="检查命令">
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item name="expected_result" label="期望结果">
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item name="fix_suggestion" label="修复建议">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Card>
    </Col>
  </Row>
</Modal>
```

### 3. 执行检查进度对话框

**用途**: 显示检查执行进度和实时日志
**尺寸**: 900px 宽度，600px 高度
**布局**: 上部进度信息，下部日志输出

```typescript
// 执行进度对话框
<Modal
  title="执行检查"
  open={executeModalVisible}
  onCancel={handleExecuteCancel}
  width={900}
  footer={[
    <Button key="stop" danger onClick={stopExecution} disabled={!executing}>
      停止检查
    </Button>,
    <Button key="close" onClick={handleExecuteCancel} disabled={executing}>
      关闭
    </Button>
  ]}
>
  <div className="execution-progress">
    <Row gutter={16}>
      <Col span={8}>
        <Statistic title="总检查项" value={totalChecks} />
      </Col>
      <Col span={8}>
        <Statistic title="已完成" value={completedChecks} />
      </Col>
      <Col span={8}>
        <Statistic title="发现问题" value={foundIssues} />
      </Col>
    </Row>

    <Progress
      percent={Math.round((completedChecks / totalChecks) * 100)}
      status={executing ? "active" : "success"}
      style={{ marginTop: 16 }}
    />

    <div className="current-task" style={{ marginTop: 16 }}>
      <Text strong>当前任务: </Text>
      <Text>{currentTask}</Text>
    </div>

    <Divider />

    <div className="log-output">
      <div className="log-header">
        <Text strong>执行日志</Text>
        <Button
          type="text"
          size="small"
          icon={<ClearOutlined />}
          onClick={clearLogs}
        >
          清空
        </Button>
      </div>
      <div className="log-content">
        <pre style={{ 
          height: 300, 
          overflow: 'auto', 
          backgroundColor: '#f5f5f5',
          padding: 12,
          fontSize: 12,
          fontFamily: 'Monaco, Consolas, monospace'
        }}>
          {executionLogs.join('\n')}
        </pre>
      </div>
    </div>
  </div>
</Modal>
```

## 组件规范

### 颜色规范
- **主色调**: #1890ff (Ant Design 默认蓝色)
- **成功色**: #52c41a (绿色)
- **警告色**: #faad14 (橙色)  
- **错误色**: #f5222d (红色)
- **信息色**: #1890ff (蓝色)

### 字体规范
- **主字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto
- **代码字体**: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace
- **字号**: 12px(小), 14px(正文), 16px(标题), 20px(大标题)

### 间距规范
- **基础间距**: 8px
- **常用间距**: 8px, 16px, 24px, 32px
- **卡片内边距**: 24px
- **表格行高**: 54px

### 响应式断点
- **xs**: < 576px
- **sm**: ≥ 576px  
- **md**: ≥ 768px
- **lg**: ≥ 992px
- **xl**: ≥ 1200px
- **xxl**: ≥ 1600px

## 交互流程设计

### 主要操作流程

```mermaid
flowchart TD
    A[启动应用] --> B[加载主界面]
    B --> C{是否有服务器配置}
    C -->|否| D[显示添加服务器引导]
    C -->|是| E[显示仪表板]
    
    D --> F[添加服务器对话框]
    F --> G[测试连接]
    G --> H{连接成功?}
    H -->|是| I[保存服务器配置]
    H -->|否| J[显示错误信息]
    J --> F
    I --> E
    
    E --> K[选择检查操作]
    K --> L[配置检查项]
    L --> M[选择目标服务器]
    M --> N[执行检查]
    N --> O[显示进度]
    O --> P[检查完成]
    P --> Q[查看结果]
    Q --> R[生成报告]
```

### 错误处理流程

```mermaid
flowchart TD
    A[操作执行] --> B{是否成功}
    B -->|成功| C[显示成功消息]
    B -->|失败| D[捕获错误]
    D --> E{错误类型}
    E -->|网络错误| F[显示网络错误提示]
    E -->|认证错误| G[显示认证失败提示]
    E -->|权限错误| H[显示权限不足提示]
    E -->|其他错误| I[显示通用错误提示]
    
    F --> J[提供重试选项]
    G --> K[提供重新配置选项]
    H --> L[提供权限说明]
    I --> M[提供错误详情]
    
    J --> A
    K --> N[打开配置对话框]
    L --> O[显示帮助信息]
    M --> P[记录错误日志]
```

## 开发建议

### 1. 项目结构建议
```
frontend/
├── src/
│   ├── components/          # 通用组件
│   │   ├── Layout/         # 布局组件
│   │   ├── ServerModal/    # 服务器配置对话框
│   │   ├── ConfigModal/    # 检查配置对话框
│   │   └── ExecuteModal/   # 执行进度对话框
│   ├── pages/              # 页面组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── ServerManage/   # 服务器管理
│   │   ├── CheckConfig/    # 检查配置
│   │   ├── ExecuteCheck/   # 执行检查
│   │   ├── ResultView/     # 结果查看
│   │   └── ReportManage/   # 报告管理
│   ├── services/           # API服务
│   ├── utils/              # 工具函数
│   ├── styles/             # 样式文件
│   └── types/              # TypeScript类型定义
```

### 2. 状态管理建议
- 使用 React Context + useReducer 管理全局状态
- 服务器列表、检查配置、执行状态等核心数据需要全局管理
- 对话框状态可以使用局部状态管理

### 3. 性能优化建议
- 使用 React.memo 优化组件渲染
- 大数据表格使用虚拟滚动
- 图表组件按需加载
- 合理使用 useMemo 和 useCallback

### 4. 用户体验建议
- 提供操作反馈（Loading、Success、Error状态）
- 重要操作需要确认对话框
- 支持键盘快捷键
- 提供操作撤销功能
- 数据自动保存和恢复

这个UI设计文档为Linux远程等保合规检查工具提供了完整的界面设计指导，基于现代化的技术栈和成熟的设计系统，确保工具的专业性和易用性。
