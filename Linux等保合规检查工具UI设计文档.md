# Linux远程等保合规检查工具 UI布局设计

## UI界面设计图

### 1. 主界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                           🔔 👤 admin ⚙️ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 📊 仪表板 │ 🖥️ 服务器管理 │ ⚙️ 检查配置 │ ▶️ 执行检查 │ 📋 结果查看 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域                                              │
│                     (页面内容显示区域)                                           │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 👤 当前用户: admin │ 📦 版本: v1.0.0                    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 2. 仪表板页面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 仪表板                                                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                │
│ │   🖥️        │ │    ✅       │ │    ⚠️       │ │    🛡️       │                │
│ │ 服务器总数   │ │  检查任务   │ │  风险项目   │ │   合规率    │                │
│ │     12      │ │      8      │ │      3      │ │    85%     │                │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────┐ ┌─────────────────────────────┐                │
│ │        快速操作              │ │        最近活动              │                │
│ │                             │ │                             │                │
│ │ ➕ 添加服务器                │ │ 🔍 检查任务完成 - 2分钟前    │                │
│ │ ▶️ 执行检查                  │ │ ➕ 新增服务器 - 10分钟前     │                │
│ │ 📄 生成报告                  │ │ 📄 报告生成 - 1小时前        │                │
│ │                             │ │ ⚠️ 发现风险项 - 2小时前      │                │
│ └─────────────────────────────┘ └─────────────────────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                          📈 检查结果趋势图                                   │ │
│ │                                                                             │ │
│ │     ▲                                                                       │ │
│ │  100│     ●                                                                 │ │
│ │   80│   ●   ●                                                               │ │
│ │   60│ ●       ●                                                             │ │
│ │   40│           ●                                                           │ │
│ │   20│             ●                                                         │ │
│ │    0└─────────────────────────────────────────────────────────────────────▶ │ │
│ │     1月  2月  3月  4月  5月  6月                                            │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 3. 服务器连接配置对话框

```
                    ┌─────────────────────────────────────┐
                    │        📝 添加服务器                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 基本信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 服务器名称: [Web服务器01_______] │ │
                    │ │ IP地址/域名: [192.168.1.100____] │ │
                    │ │ SSH端口: [22] 系统版本: [CentOS] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 认证信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 认证方式: ⚫ 密码认证 ⚪ 密钥认证 │ │
                    │ │ 用户名: [root_______________]   │ │
                    │ │ 密码: [******************]     │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 高级设置                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 连接超时: [30秒] Sudo密码: [可选] │ │
                    │ │ 描述: [________________________] │ │
                    │ │      [________________________] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │  🔍 测试连接   ❌ 取消   ✅ 确定   │
                    └─────────────────────────────────────┘
```

### 4. 检查项配置对话框

```mermaid
graph TB
    subgraph "检查项配置对话框 800x600px"
        subgraph "对话框标题"
            A["⚙️ 检查项配置"]
        end

        subgraph "左侧分类树区域 300px"
            B["检查分类树<br/>📁 系统安全<br/>├─ ☑️ 用户权限检查<br/>├─ ☑️ 密码策略检查<br/>└─ ☐ 系统配置检查<br/>📁 网络安全<br/>├─ ☑️ 防火墙配置<br/>└─ ☐ 端口扫描<br/>📁 应用安全<br/>└─ ☑️ 服务配置检查"]
        end

        subgraph "右侧详情配置区域 500px"
            C["检查项详情配置<br/>检查项名称: [用户权限检查_______]<br/>严重级别: [高危 ▼]<br/>检查命令: [cat /etc/passwd | grep root]<br/>[_________________________]<br/>期望结果: [root:x:0:0:root:/root:/bin/bash]<br/>修复建议: [检查root用户配置是否正确]<br/>[_________________________]"]
        end

        subgraph "底部操作区域"
            D["� 导入模板    �💾 保存配置    ❌ 取消    ✅ 确定"]
        end
    end

    style A fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style B fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style C fill:#fafafa,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```

### 5. 执行检查进度对话框

```mermaid
graph TB
    subgraph "执行检查对话框 900x600px"
        subgraph "对话框标题"
            A["▶️ 执行检查进度"]
        end

        subgraph "进度统计区域"
            B["总检查项: 45    已完成: 32    发现问题: 5<br/>████████████████████████████████░░░░░░░░ 71%<br/>当前任务: 正在检查SSH配置安全性..."]
        end

        subgraph "实时日志区域 400px高度"
            C["📝 执行日志                                    🗑️ 清空日志<br/>┌─────────────────────────────────────────────────┐<br/>│[2024-01-15 10:30:01] 开始检查用户权限...          │<br/>│[2024-01-15 10:30:02] ✅ 用户权限检查通过          │<br/>│[2024-01-15 10:30:03] 开始检查密码策略...          │<br/>│[2024-01-15 10:30:04] ⚠️ 发现密码策略问题          │<br/>│[2024-01-15 10:30:05] 开始检查SSH配置...           │<br/>│[2024-01-15 10:30:06] 正在验证SSH端口配置...       │<br/>│[滚动显示更多日志内容...]                        │<br/>└─────────────────────────────────────────────────┘"]
        end

        subgraph "操作按钮区域"
            D["⏸️ 暂停检查    ⏹️ 停止检查    ❌ 关闭"]
        end
    end

    style A fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style B fill:#f6ffed,stroke:#52c41a,stroke-width:2px
    style C fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```

### 6. 检查结果展示页面

```mermaid
graph TB
    subgraph "检查结果页面"
        subgraph "结果概览统计卡片"
            A["✅ 通过项目: 42  |  ⚠️ 警告项目: 8  |  ❌ 失败项目: 3  |  📊 总体评分: 85分"]
        end

        subgraph "筛选和搜索工具栏"
            B["服务器: [全部 ▼] | 严重级别: [全部 ▼] | 状态: [全部 ▼] | 🔍 [搜索关键词_____]"]
        end

        subgraph "结果详情表格区域"
            C["┌─────────────────────────────────────────────────────────────────────────┐<br/>│检查项目        │服务器        │级别│状态  │检查时间  │操作      │<br/>├─────────────────────────────────────────────────────────────────────────┤<br/>│用户权限检查    │Web服务器01   │高危│❌失败│10:30:01 │查看详情  │<br/>│SSH配置检查     │Web服务器01   │中危│⚠️警告│10:30:02 │查看详情  │<br/>│防火墙配置      │数据库服务器  │高危│✅通过│10:30:03 │查看详情  │<br/>│密码策略检查    │应用服务器01  │中危│⚠️警告│10:30:04 │查看详情  │<br/>│服务配置检查    │文件服务器    │低危│✅通过│10:30:05 │查看详情  │<br/>└─────────────────────────────────────────────────────────────────────────┘"]
        end

        subgraph "操作按钮区域"
            D["📄 生成报告    📤 导出结果    🔄 重新检查    📧 发送邮件"]
        end
    end

    style A fill:#e6f7ff,stroke:#1890ff,stroke-width:2px
    style B fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style C fill:#ffffff,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```


