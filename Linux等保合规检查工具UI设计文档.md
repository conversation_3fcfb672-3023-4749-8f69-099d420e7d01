# Linux远程等保合规检查工具 UI布局设计

## UI界面设计图

### 1. 主界面布局

```mermaid
graph TB
    subgraph "主窗口 1200x800px"
        subgraph "顶部标题栏 64px"
            A1["🛡️ Linux等保合规检查工具"]
            A2["🔔 | 👤 admin | ⚙️"]
        end

        subgraph "导航菜单栏 48px"
            B["📊 仪表板 | 🖥️ 服务器管理 | ⚙️ 检查配置 | ▶️ 执行检查 | 📋 结果查看"]
        end

        subgraph "主内容区域"
            C["页面内容显示区域"]
        end

        subgraph "状态栏 32px"
            D["🟢 连接状态: 已连接 | 👤 当前用户: admin | 📦 版本: v1.0.0"]
        end
    end

    style A1 fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style A2 fill:#f0f0f0,stroke:#333,stroke-width:2px
    style B fill:#f5f5f5,stroke:#333,stroke-width:2px
    style C fill:#ffffff,stroke:#333,stroke-width:2px
    style D fill:#e6f7ff,stroke:#333,stroke-width:2px
```

### 2. 仪表板页面布局

```mermaid
graph TB
    subgraph "仪表板内容区域"
        subgraph "统计卡片区 - 第一行"
            A1["🖥️<br/>服务器总数<br/>12"]
            A2["✅<br/>检查任务<br/>8"]
            A3["⚠️<br/>风险项目<br/>3"]
            A4["🛡️<br/>合规率<br/>85%"]
        end

        subgraph "操作和活动区 - 第二行"
            B1["快速操作<br/>➕ 添加服务器<br/>▶️ 执行检查<br/>📄 生成报告"]
            B2["最近活动<br/>🔍 检查完成 - 2分钟前<br/>➕ 新增服务器 - 10分钟前<br/>📄 报告生成 - 1小时前"]
        end

        subgraph "图表区域 - 第三行"
            C["📈 检查结果趋势图 | 📊 风险分布图"]
        end
    end

    style A1 fill:#e6f7ff,stroke:#1890ff,stroke-width:2px
    style A2 fill:#f6ffed,stroke:#52c41a,stroke-width:2px
    style A3 fill:#fff2e8,stroke:#faad14,stroke-width:2px
    style A4 fill:#f9f0ff,stroke:#722ed1,stroke-width:2px
    style B1 fill:#f0f9ff,stroke:#333,stroke-width:2px
    style B2 fill:#f0f9ff,stroke:#333,stroke-width:2px
    style C fill:#fafafa,stroke:#333,stroke-width:2px
```

### 3. 服务器连接配置对话框

```mermaid
graph TB
    subgraph "服务器配置对话框 600x500px"
        subgraph "对话框标题"
            A["📝 添加服务器"]
        end

        subgraph "基本信息区域"
            B1["服务器名称: [________________]"]
            B2["IP地址/域名: [________________]"]
            B3["SSH端口: [22___] | 系统版本: [CentOS 7.9____]"]
        end

        subgraph "认证信息区域"
            C1["认证方式: ⚪ 密码认证  ⚪ 密钥认证"]
            C2["用户名: [root____________]"]
            C3["密码: [****************]"]
        end

        subgraph "高级设置区域"
            D1["连接超时: [30秒] | Sudo密码: [可选_______]"]
            D2["描述: [_________________________]<br/>[_________________________]"]
        end

        subgraph "操作按钮区域"
            E["🔍 测试连接    ❌ 取消    ✅ 确定"]
        end
    end

    style A fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style B1 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style B2 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style B3 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style C1 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style C2 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style C3 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style D1 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style D2 fill:#fafafa,stroke:#d9d9d9,stroke-width:1px
    style E fill:#f0f0f0,stroke:#333,stroke-width:2px
```

### 4. 检查项配置对话框

```mermaid
graph TB
    subgraph "检查项配置对话框 800x600px"
        subgraph "对话框标题"
            A["⚙️ 检查项配置"]
        end

        subgraph "左侧分类树区域 300px"
            B["检查分类树<br/>📁 系统安全<br/>├─ ☑️ 用户权限检查<br/>├─ ☑️ 密码策略检查<br/>└─ ☐ 系统配置检查<br/>📁 网络安全<br/>├─ ☑️ 防火墙配置<br/>└─ ☐ 端口扫描<br/>📁 应用安全<br/>└─ ☑️ 服务配置检查"]
        end

        subgraph "右侧详情配置区域 500px"
            C["检查项详情配置<br/>检查项名称: [用户权限检查_______]<br/>严重级别: [高危 ▼]<br/>检查命令: [cat /etc/passwd | grep root]<br/>[_________________________]<br/>期望结果: [root:x:0:0:root:/root:/bin/bash]<br/>修复建议: [检查root用户配置是否正确]<br/>[_________________________]"]
        end

        subgraph "底部操作区域"
            D["� 导入模板    �💾 保存配置    ❌ 取消    ✅ 确定"]
        end
    end

    style A fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style B fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style C fill:#fafafa,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```

### 5. 执行检查进度对话框

```mermaid
graph TB
    subgraph "执行检查对话框 900x600px"
        subgraph "对话框标题"
            A["▶️ 执行检查进度"]
        end

        subgraph "进度统计区域"
            B["总检查项: 45    已完成: 32    发现问题: 5<br/>████████████████████████████████░░░░░░░░ 71%<br/>当前任务: 正在检查SSH配置安全性..."]
        end

        subgraph "实时日志区域 400px高度"
            C["📝 执行日志                                    🗑️ 清空日志<br/>┌─────────────────────────────────────────────────┐<br/>│[2024-01-15 10:30:01] 开始检查用户权限...          │<br/>│[2024-01-15 10:30:02] ✅ 用户权限检查通过          │<br/>│[2024-01-15 10:30:03] 开始检查密码策略...          │<br/>│[2024-01-15 10:30:04] ⚠️ 发现密码策略问题          │<br/>│[2024-01-15 10:30:05] 开始检查SSH配置...           │<br/>│[2024-01-15 10:30:06] 正在验证SSH端口配置...       │<br/>│[滚动显示更多日志内容...]                        │<br/>└─────────────────────────────────────────────────┘"]
        end

        subgraph "操作按钮区域"
            D["⏸️ 暂停检查    ⏹️ 停止检查    ❌ 关闭"]
        end
    end

    style A fill:#1890ff,color:#fff,stroke:#333,stroke-width:2px
    style B fill:#f6ffed,stroke:#52c41a,stroke-width:2px
    style C fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```

### 6. 检查结果展示页面

```mermaid
graph TB
    subgraph "检查结果页面"
        subgraph "结果概览统计卡片"
            A["✅ 通过项目: 42  |  ⚠️ 警告项目: 8  |  ❌ 失败项目: 3  |  📊 总体评分: 85分"]
        end

        subgraph "筛选和搜索工具栏"
            B["服务器: [全部 ▼] | 严重级别: [全部 ▼] | 状态: [全部 ▼] | 🔍 [搜索关键词_____]"]
        end

        subgraph "结果详情表格区域"
            C["┌─────────────────────────────────────────────────────────────────────────┐<br/>│检查项目        │服务器        │级别│状态  │检查时间  │操作      │<br/>├─────────────────────────────────────────────────────────────────────────┤<br/>│用户权限检查    │Web服务器01   │高危│❌失败│10:30:01 │查看详情  │<br/>│SSH配置检查     │Web服务器01   │中危│⚠️警告│10:30:02 │查看详情  │<br/>│防火墙配置      │数据库服务器  │高危│✅通过│10:30:03 │查看详情  │<br/>│密码策略检查    │应用服务器01  │中危│⚠️警告│10:30:04 │查看详情  │<br/>│服务配置检查    │文件服务器    │低危│✅通过│10:30:05 │查看详情  │<br/>└─────────────────────────────────────────────────────────────────────────┘"]
        end

        subgraph "操作按钮区域"
            D["📄 生成报告    📤 导出结果    🔄 重新检查    📧 发送邮件"]
        end
    end

    style A fill:#e6f7ff,stroke:#1890ff,stroke-width:2px
    style B fill:#f5f5f5,stroke:#d9d9d9,stroke-width:2px
    style C fill:#ffffff,stroke:#d9d9d9,stroke-width:2px
    style D fill:#f0f0f0,stroke:#333,stroke-width:2px
```


