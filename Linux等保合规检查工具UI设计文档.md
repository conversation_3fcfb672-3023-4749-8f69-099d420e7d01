# Linux远程等保合规检查工具 UI布局设计

## UI界面设计图

### 1. 主界面布局

```mermaid
graph TB
    subgraph "主窗口"
        A["🛡️ Linux等保合规检查工具"]
        B["📊 仪表板 | 🖥️ 服务器 | ⚙️ 配置 | ▶️ 检查 | 📋 结果"]
        C["主内容区域"]
        D["状态栏: 🟢 已连接 | 用户: admin"]
    end

    style A fill:#1890ff,color:#fff
    style B fill:#f0f0f0
    style C fill:#f9f9f9
    style D fill:#e6f7ff
```

### 2. 服务器连接对话框

```mermaid
graph TB
    subgraph "添加服务器"
        A["服务器名称: [输入框]"]
        B["IP地址: [输入框]"]
        C["端口: [22]"]
        D["用户名: [输入框]"]
        E["密码: [密码框]"]
        F["🔍 测试连接 | ❌ 取消 | ✅ 确定"]
    end

    style F fill:#52c41a,color:#fff
```

### 3. 检查配置对话框

```mermaid
graph TB
    subgraph "检查配置"
        A["📁 系统安全<br/>├─ 用户权限<br/>├─ 密码策略<br/>└─ 系统配置"]
        B["📁 网络安全<br/>├─ 防火墙<br/>└─ 端口扫描"]
        C["检查项详情:<br/>名称: [输入框]<br/>级别: [下拉选择]<br/>命令: [文本域]"]
        D["💾 保存 | ❌ 取消"]
    end

    style D fill:#1890ff,color:#fff
```

### 4. 执行检查进度

```mermaid
graph TB
    subgraph "执行检查"
        A["进度: ████████░░ 80%"]
        B["当前: 检查SSH配置..."]
        C["日志:<br/>[10:30:01] ✅ 用户权限通过<br/>[10:30:02] ⚠️ 密码策略警告<br/>[10:30:03] 检查SSH配置..."]
        D["⏹️ 停止 | ❌ 关闭"]
    end

    style A fill:#52c41a,color:#fff
    style D fill:#f5222d,color:#fff
```

### 5. 检查结果页面

```mermaid
graph TB
    subgraph "检查结果"
        A["✅ 通过: 42 | ⚠️ 警告: 8 | ❌ 失败: 3 | 📊 评分: 85"]
        B["服务器列表:<br/>Web服务器01 - 用户权限检查 - ❌失败<br/>数据库服务器 - SSH配置 - ⚠️警告<br/>应用服务器 - 防火墙配置 - ✅通过"]
        C["📄 生成报告 | 📤 导出 | 🔄 重新检查"]
    end

    style A fill:#e6f7ff
    style C fill:#52c41a,color:#fff
```


