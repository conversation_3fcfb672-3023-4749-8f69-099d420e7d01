# Linux等保合规检查工具 UI界面设计图

## 1. 主界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                           🔔 👤 admin ⚙️ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 📊 仪表板 │ 🖥️ 服务器管理 │ ⚙️ 检查配置 │ ▶️ 执行检查 │ 📋 结果查看 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域                                              │
│                     (页面内容显示区域)                                           │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 👤 当前用户: admin │ 📦 版本: v1.0.0                    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 仪表板页面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 仪表板                                                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                │
│ │   🖥️        │ │    ✅       │ │    ⚠️       │ │    🛡️       │                │
│ │ 服务器总数   │ │  检查任务   │ │  风险项目   │ │   合规率    │                │
│ │     12      │ │      8      │ │      3      │ │    85%     │                │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────┐ ┌─────────────────────────────┐                │
│ │        快速操作              │ │        最近活动              │                │
│ │                             │ │                             │                │
│ │ ➕ 添加服务器                │ │ 🔍 检查任务完成 - 2分钟前    │                │
│ │ ▶️ 执行检查                  │ │ ➕ 新增服务器 - 10分钟前     │                │
│ │ 📄 生成报告                  │ │ 📄 报告生成 - 1小时前        │                │
│ │                             │ │ ⚠️ 发现风险项 - 2小时前      │                │
│ └─────────────────────────────┘ └─────────────────────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                          📈 检查结果趋势图                                   │ │
│ │                                                                             │ │
│ │     ▲                                                                       │ │
│ │  100│     ●                                                                 │ │
│ │   80│   ●   ●                                                               │ │
│ │   60│ ●       ●                                                             │ │
│ │   40│           ●                                                           │ │
│ │   20│             ●                                                         │ │
│ │    0└─────────────────────────────────────────────────────────────────────▶ │ │
│ │     1月  2月  3月  4月  5月  6月                                            │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 服务器连接配置对话框

```
                    ┌─────────────────────────────────────┐
                    │        📝 添加服务器                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 基本信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 服务器名称: [Web服务器01_______] │ │
                    │ │ IP地址/域名: [*************____] │ │
                    │ │ SSH端口: [22] 系统版本: [CentOS] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 认证信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 认证方式: ⚫ 密码认证 ⚪ 密钥认证 │ │
                    │ │ 用户名: [root_______________]   │ │
                    │ │ 密码: [******************]     │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 高级设置                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 连接超时: [30秒] Sudo密码: [可选] │ │
                    │ │ 描述: [________________________] │ │
                    │ │      [________________________] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │  🔍 测试连接   ❌ 取消   ✅ 确定   │
                    └─────────────────────────────────────┘
```

## 4. 检查项配置对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           ⚙️ 检查项配置                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐ │ ┌─────────────────────────────────────────────┐ │
│ │    检查分类树        │ │ │            检查项详情配置                    │ │
│ │                     │ │ │                                             │ │
│ │ 📁 系统安全          │ │ │ 检查项名称: [用户权限检查_______________]    │ │
│ │ ├─ ☑️ 用户权限检查   │ │ │                                             │ │
│ │ ├─ ☑️ 密码策略检查   │ │ │ 严重级别: [高危 ▼]                          │ │
│ │ └─ ☐ 系统配置检查   │ │ │                                             │ │
│ │                     │ │ │ 检查命令:                                   │ │
│ │ 📁 网络安全          │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ├─ ☑️ 防火墙配置     │ │ │ │ cat /etc/passwd | grep root             │ │ │
│ │ └─ ☐ 端口扫描       │ │ │ │ ls -la /etc/shadow                      │ │ │
│ │                     │ │ │ └─────────────────────────────────────────┘ │ │
│ │ 📁 应用安全          │ │ │                                             │ │
│ │ └─ ☑️ 服务配置检查   │ │ │ 期望结果: [root:x:0:0:root:/root:/bin/bash] │ │
│ │                     │ │ │                                             │ │
│ │                     │ │ │ 修复建议:                                   │ │
│ │                     │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │                     │ │ │ │ 检查root用户配置是否正确，确保权限设置   │ │ │
│ │                     │ │ │ │ 符合等保要求                            │ │ │
│ │                     │ │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────┘ │ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│        📋 导入模板    💾 保存配置    ❌ 取消    ✅ 确定                      │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 执行检查进度对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        ▶️ 执行检查进度                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 总检查项: 45    已完成: 32    发现问题: 5                                    │
│ ████████████████████████████████░░░░░░░░ 71%                                │
│ 当前任务: 正在检查SSH配置安全性...                                           │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📝 执行日志                                    🗑️ 清空日志                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │[2024-01-15 10:30:01] 开始检查用户权限...                                │ │
│ │[2024-01-15 10:30:02] ✅ 用户权限检查通过                                │ │
│ │[2024-01-15 10:30:03] 开始检查密码策略...                                │ │
│ │[2024-01-15 10:30:04] ⚠️ 发现密码策略问题                                │ │
│ │[2024-01-15 10:30:05] 开始检查SSH配置...                                 │ │
│ │[2024-01-15 10:30:06] 正在验证SSH端口配置...                             │ │
│ │[2024-01-15 10:30:07] 检查防火墙规则...                                  │ │
│ │[2024-01-15 10:30:08] ✅ 防火墙配置正常                                  │ │
│ │[滚动显示更多日志内容...]                                                │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│           ⏸️ 暂停检查    ⏹️ 停止检查    ❌ 关闭                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. 检查结果展示页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 检查结果                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ ✅ 通过项目: 42  │  ⚠️ 警告项目: 8  │  ❌ 失败项目: 3  │  📊 总体评分: 85分 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 服务器: [全部 ▼] │ 严重级别: [全部 ▼] │ 状态: [全部 ▼] │ 🔍 [搜索关键词___] │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │检查项目        │服务器        │级别│状态  │检查时间  │操作      │         │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │用户权限检查    │Web服务器01   │高危│❌失败│10:30:01 │查看详情  │         │ │
│ │SSH配置检查     │Web服务器01   │中危│⚠️警告│10:30:02 │查看详情  │         │ │
│ │防火墙配置      │数据库服务器  │高危│✅通过│10:30:03 │查看详情  │         │ │
│ │密码策略检查    │应用服务器01  │中危│⚠️警告│10:30:04 │查看详情  │         │ │
│ │服务配置检查    │文件服务器    │低危│✅通过│10:30:05 │查看详情  │         │ │
│ │端口扫描检查    │Web服务器01   │中危│❌失败│10:30:06 │查看详情  │         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│        📄 生成报告    📤 导出结果    🔄 重新检查    📧 发送邮件              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. 服务器管理页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🖥️ 服务器管理                                                               │
│ 管理需要进行等保合规检查的Linux服务器                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│        🔄 刷新    ➕ 添加服务器    🗑️ 批量删除    📤 导出列表               │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │☑️│服务器名称    │IP地址        │端口│状态  │最后检查  │操作        │     │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │☑️│Web服务器01   │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│数据库服务器  │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│应用服务器01  │************* │22  │🔴离线│2024-01-14│编辑|删除|测试│     │ │
│ │☑️│文件服务器    │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│备份服务器    │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 共 25 条记录 │ 每页显示: [10▼] │ ◀️ 1 2 3 4 5 ▶️                           │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. 检查结果详情对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                      🔍 检查结果详情 - 用户权限检查                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ 基本信息                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 检查项目: 用户权限检查                    服务器: Web服务器01             │ │
│ │ 严重级别: 高危                           状态: ❌ 失败                    │ │
│ │ 检查时间: 2024-01-15 10:30:01            执行用时: 2.3秒                 │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 检查命令                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ cat /etc/passwd | grep root                                             │ │
│ │ ls -la /etc/shadow                                                      │ │
│ │ id root                                                                 │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 实际结果                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ root:x:0:0:root:/root:/bin/bash                                         │ │
│ │ -rw-r----- 1 <USER> <GROUP> 1234 Jan 15 10:30 /etc/shadow                 │ │
│ │ uid=0(root) gid=0(root) groups=0(root)                                 │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 期望结果                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ root用户应该存在且配置正确                                               │ │
│ │ /etc/shadow文件权限应为640                                              │ │
│ │ root用户UID应为0                                                        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 问题描述                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ ❌ /etc/shadow文件权限过于宽松，当前为644，应设置为640                   │ │
│ │ ⚠️ root用户shell为/bin/bash，建议评估是否需要限制                       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 修复建议                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 1. 执行命令: chmod 640 /etc/shadow                                      │ │
│ │ 2. 验证权限: ls -la /etc/shadow                                         │ │
│ │ 3. 考虑将root shell改为/sbin/nologin以提高安全性                        │ │
│ │ 4. 定期检查系统用户权限配置                                              │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│        📋 复制命令    🔄 重新检查    📝 添加备注    ❌ 关闭                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 9. 报告生成配置对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           📄 生成检查报告                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│ 报告基本信息                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 报告标题: [Linux等保合规检查报告_________________]                       │ │
│ │ 报告类型: [完整报告 ▼] [简要报告] [问题报告] [合规报告]                  │ │
│ │ 检查时间: 2024-01-15 10:30:01 - 10:45:23                               │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 包含内容                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ ☑️ 执行摘要          ☑️ 检查结果统计      ☑️ 风险等级分布               │ │
│ │ ☑️ 服务器信息        ☑️ 详细检查结果      ☑️ 修复建议                   │ │
│ │ ☑️ 合规性评估        ☑️ 趋势分析图表      ☐ 技术附录                   │ │
│ │ ☑️ 问题清单          ☐ 执行日志           ☐ 原始数据                   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 筛选条件                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 服务器范围: ☑️ 全部服务器  ☐ 选定服务器                                 │ │
│ │ 严重级别:   ☑️ 高危  ☑️ 中危  ☑️ 低危  ☑️ 信息                         │ │
│ │ 检查状态:   ☑️ 失败  ☑️ 警告  ☐ 通过                                   │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 输出格式                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 文件格式: ⚫ PDF  ⚪ Word  ⚪ Excel  ⚪ HTML                              │ │
│ │ 保存路径: [/home/<USER>/reports/___________________] 📁 浏览               │ │
│ │ 文件名:   [compliance_report_20240115.pdf_______]                      │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│        📄 预览报告    💾 生成报告    📧 发送邮件    ❌ 取消                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 10. 系统设置对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            ⚙️ 系统设置                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ │ ┌─────────────────────────────────────────────────┐ │
│ │   设置分类       │ │ │                设置详情                          │ │
│ │                 │ │ │                                                 │ │
│ │ 🔧 常规设置      │ │ │ 语言设置: [中文 ▼] [English] [日本語]            │ │
│ │ 🔐 安全设置      │ │ │                                                 │ │
│ │ 📊 检查设置      │ │ │ 主题设置: ⚫ 浅色主题  ⚪ 深色主题  ⚪ 自动       │ │
│ │ 📧 通知设置      │ │ │                                                 │ │
│ │ 💾 备份设置      │ │ │ 自动保存: ☑️ 启用 间隔: [5分钟 ▼]               │ │
│ │ 🔄 更新设置      │ │ │                                                 │ │
│ │ ℹ️ 关于         │ │ │ 日志级别: [INFO ▼] [DEBUG] [WARN] [ERROR]       │ │
│ │                 │ │ │                                                 │ │
│ │                 │ │ │ 最大日志文件: [100MB____] 保留天数: [30天___]    │ │
│ │                 │ │ │                                                 │ │
│ │                 │ │ │ 并发检查数: [5_____] 超时时间: [300秒____]       │ │
│ │                 │ │ │                                                 │ │
│ │                 │ │ │ 默认SSH端口: [22___] 重试次数: [3____]           │ │
│ │                 │ │ │                                                 │ │
│ │                 │ │ │ ☑️ 检查完成后发送通知                           │ │
│ │                 │ │ │ ☑️ 发现高危问题时立即通知                       │ │
│ │                 │ │ │ ☐ 每日发送检查摘要                             │ │
│ └─────────────────┘ │ └─────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│        🔄 重置默认    💾 保存设置    📤 导出配置    ❌ 取消                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 11. 用户管理对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                            👤 用户管理                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│        ➕ 添加用户    🔄 刷新    🗑️ 删除用户    📤 导出用户列表              │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │用户名      │角色      │状态    │最后登录        │操作            │       │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │admin       │管理员    │🟢 活跃 │2024-01-15 10:30│编辑|禁用|重置密码│       │ │
│ │operator    │操作员    │🟢 活跃 │2024-01-15 09:15│编辑|禁用|重置密码│       │ │
│ │auditor     │审计员    │🟡 离线 │2024-01-14 16:20│编辑|启用|重置密码│       │ │
│ │guest       │访客      │🔴 禁用 │2024-01-10 14:30│编辑|启用|重置密码│       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 角色权限说明                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 👑 管理员: 所有功能权限，用户管理，系统设置                              │ │
│ │ 🔧 操作员: 服务器管理，执行检查，查看结果，生成报告                      │ │
│ │ 📊 审计员: 查看检查结果，生成报告，导出数据                              │ │
│ │ 👁️ 访客:   仅查看检查结果和报告                                         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│                              ❌ 关闭                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 12. 添加用户对话框

```
                    ┌─────────────────────────────────────┐
                    │        👤 添加新用户                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 基本信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 用户名: [newuser____________]   │ │
                    │ │ 姓名:   [张三________________]   │ │
                    │ │ 邮箱:   [user@company.com____]  │ │
                    │ │ 电话:   [13800138000_________]   │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 账户设置                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 角色:   [操作员 ▼]              │ │
                    │ │ 密码:   [******************]   │ │
                    │ │ 确认:   [******************]   │ │
                    │ │ 状态:   ⚫ 启用  ⚪ 禁用        │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 权限设置                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ ☑️ 服务器管理                   │ │
                    │ │ ☑️ 执行检查                     │ │
                    │ │ ☑️ 查看结果                     │ │
                    │ │ ☑️ 生成报告                     │ │
                    │ │ ☐ 用户管理                     │ │
                    │ │ ☐ 系统设置                     │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │    💾 保存用户   ❌ 取消           │
                    └─────────────────────────────────────┘
```

## 13. 检查模板管理对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          📋 检查模板管理                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│    ➕ 新建模板    📥 导入模板    📤 导出模板    🗑️ 删除模板    🔄 刷新      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │模板名称          │类型    │检查项│创建时间      │操作          │         │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │等保2.0基础模板   │系统    │45   │2024-01-10    │编辑|复制|删除 │         │ │
│ │网络安全检查模板  │网络    │23   │2024-01-12    │编辑|复制|删除 │         │ │
│ │数据库安全模板    │应用    │18   │2024-01-13    │编辑|复制|删除 │         │ │
│ │Web应用安全模板   │应用    │32   │2024-01-14    │编辑|复制|删除 │         │ │
│ │自定义检查模板    │混合    │67   │2024-01-15    │编辑|复制|删除 │         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 模板预览                                                                     │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 模板名称: 等保2.0基础模板                                                │ │
│ │ 描述: 基于等保2.0标准的基础安全检查项目                                  │ │
│ │ 包含检查项:                                                             │ │
│ │ • 用户权限检查 (高危)                                                   │ │
│ │ • 密码策略检查 (中危)                                                   │ │
│ │ • SSH配置检查 (高危)                                                    │ │
│ │ • 防火墙配置检查 (中危)                                                 │ │
│ │ • 系统日志检查 (低危)                                                   │ │
│ │ ... 共45项检查                                                          │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│                              ❌ 关闭                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 14. 定时任务配置对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          ⏰ 定时任务配置                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│    ➕ 新建任务    ▶️ 启动任务    ⏸️ 暂停任务    🗑️ 删除任务    🔄 刷新      │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │任务名称        │执行时间      │目标服务器│状态  │下次执行      │操作    │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │每日安全检查    │每天 02:00    │全部      │🟢运行│2024-01-16 02:00│编辑|停止│ │
│ │周度合规检查    │每周一 06:00  │生产环境  │🟢运行│2024-01-22 06:00│编辑|停止│ │
│ │月度全面检查    │每月1日 01:00 │全部      │🟡暂停│2024-02-01 01:00│编辑|启动│ │
│ │应急响应检查    │手动触发      │指定服务器│⚪待机│手动执行        │编辑|执行│ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 任务详情配置                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 任务名称: [每日安全检查_________________]                                │ │
│ │                                                                         │ │
│ │ 执行时间: ⚫ 定时执行  ⚪ 手动执行                                       │ │
│ │ 时间设置: [02] : [00]  重复: [每天 ▼]                                   │ │
│ │                                                                         │ │
│ │ 目标服务器: ⚫ 全部服务器  ⚪ 选定服务器  ⚪ 服务器组                    │ │
│ │                                                                         │ │
│ │ 检查模板: [等保2.0基础模板 ▼]                                           │ │
│ │                                                                         │ │
│ │ 通知设置: ☑️ 任务开始通知  ☑️ 任务完成通知  ☑️ 发现问题通知              │ │
│ │ 通知方式: ☑️ 邮件  ☑️ 系统通知  ☐ 短信                                 │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│        💾 保存任务    🧪 测试运行    ❌ 取消                                │
└─────────────────────────────────────────────────────────────────────────────┘
```

现在每个功能点都详细画出来了！包括：

✅ **主界面和核心页面**
✅ **所有对话框界面**
✅ **检查结果详情**
✅ **报告生成配置**
✅ **系统设置**
✅ **用户管理**
✅ **检查模板管理**
✅ **定时任务配置**

每个界面都有完整的功能元素、输入框、按钮、表格等，你可以清楚地看到整个系统的UI设计！
