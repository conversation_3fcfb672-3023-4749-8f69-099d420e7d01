# Linux等保合规检查工具 - UI界面设计图

## 1. 主界面布局 (固定大小: 1200x800px)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                                    👤 admin ⚙️ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🖥️ 服务器管理 │ ⚙️ 核查配置 │ ▶️ 创建任务 │ 📄 报告导出                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域 (固定显示)                                   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                                                                         │   │
│  │                        当前选中功能的内容                                │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 📦 版本: v1.0.0                                          │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 服务器管理页面 (主内容区域固定显示)

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ 🖥️ 服务器管理                                                           │
  ├─────────────────────────────────────────────────────────────────────────┤
  │        ➕ 添加服务器    🔄 刷新    🗑️ 删除                              │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │☑️│服务器名称    │IP地址        │端口│状态  │操作        │           │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │☑️│Web服务器01   │************* │22  │🟢在线│编辑|删除|测试│           │ │
  │ │☑️│数据库服务器  │************* │22  │🟢在线│编辑|删除|测试│           │ │
  │ │☑️│应用服务器01  │************* │22  │🔴离线│编辑|删除|测试│           │ │
  │ │☑️│文件服务器    │************* │22  │🟢在线│编辑|删除|测试│           │ │
  │ │☑️│备份服务器    │************* │22  │🟢在线│编辑|删除|测试│           │ │
  │ │☑️│测试服务器    │************* │22  │🟡维护│编辑|删除|测试│           │ │
  │ │☑️│开发服务器    │192.168.1.106 │22  │🟢在线│编辑|删除|测试│           │ │
  │ │☑️│监控服务器    │192.168.1.107 │22  │🟢在线│编辑|删除|测试│  ▲        │ │
  │ │☑️│日志服务器    │192.168.1.108 │22  │🟢在线│编辑|删除|测试│  █ 滚动条 │ │
  │ │☑️│缓存服务器    │192.168.1.109 │22  │🟢在线│编辑|删除|测试│  ▼        │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  └─────────────────────────────────────────────────────────────────────────┘
```

## 3. 添加服务器对话框

```
                    ┌─────────────────────────────────────┐
                    │        📝 添加服务器                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 服务器名称: [Web服务器01_________]   │
                    │ IP地址:    [*************_______]   │
                    │ SSH端口:   [22]                     │
                    │ 用户名:    [root_______________]    │
                    │                                     │
                    │ 认证方式:                           │
                    │ ⚫ 密码认证  ⚪ 密钥认证             │
                    │                                     │
                    │ 密码:      [******************]    │
                    │ 密钥文件:  [选择文件...] 📁         │
                    │                                     │
                    │  🔍 测试连接   ❌ 取消   ✅ 确定   │
                    └─────────────────────────────────────┘
```

## 4. 核查配置页面 (主内容区域固定显示)

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ ⚙️ 核查配置                                                              │
  ├─────────────────────────────────────────────────────────────────────────┤
  │                        💾 保存配置    🔄 重置默认                        │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │分类│检查项名称        │级别│状态│描述                            │   │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │系统│用户权限检查      │高危│☑️启用│检查用户权限配置                │   │ │
  │ │系统│密码策略检查      │中危│☑️启用│检查密码复杂度策略              │   │ │
  │ │系统│系统配置检查      │低危│☐禁用│检查系统基础配置                │   │ │
  │ │网络│防火墙配置检查    │高危│☑️启用│检查防火墙规则配置              │   │ │
  │ │网络│端口扫描检查      │中危│☐禁用│扫描开放端口                    │   │ │
  │ │网络│SSH配置检查       │高危│☑️启用│检查SSH安全配置                │   │ │
  │ │应用│服务配置检查      │中危│☑️启用│检查关键服务配置                │   │ │
  │ │应用│日志配置检查      │低危│☑️启用│检查日志记录配置                │  ▲│ │
  │ │应用│文件权限检查      │高危│☑️启用│检查文件权限设置                │  █│ │
  │ │安全│病毒扫描检查      │中危│☐禁用│执行病毒扫描                    │  ▼│ │
  │ │安全│进程检查          │低危│☑️启用│检查异常进程                    │   │ │
  │ │安全│网络连接检查      │中危│☑️启用│检查可疑网络连接                │   │ │
  │ │合规│审计日志检查      │高危│☑️启用│检查审计日志完整性              │   │ │
  │ │合规│备份策略检查      │中危│☑️启用│检查数据备份策略                │   │ │
  │ │合规│访问控制检查      │高危│☑️启用│检查访问控制策略                │   │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  │                                                                         │
  │ 已启用: 12项  │  高危: 6项  │  中危: 5项  │  低危: 1项                  │
  │                                                                         │
  │                    � 全部启用    ❌ 全部禁用                            │
  └─────────────────────────────────────────────────────────────────────────┘
```

## 5. 创建任务页面 (主内容区域固定显示)

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ ▶️ 创建检查任务                                                          │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────┐ │ ┌─────────────────────────────────────┐ │
  │ │       任务信息           │ │ │         检查配置预览                 │ │
  │ │                         │ │ │                                     │ │
  │ │ 任务名称:               │ │ │ 📋 已配置检查项 (共15项)             │ │
  │ │ [等保合规检查_2024-01-15] │ │ │                                     │ │
  │ │                         │ │ │ 🔴 高危检查: 5项                    │ │
  │ │ 任务描述:               │ │ │ 🟡 中危检查: 7项                    │ │
  │ │ [定期安全合规检查_____] │ │ │ 🟢 低危检查: 3项                    │ │
  │ │ [___________________] │ │ │                                     │ │
  │ │                         │ │ │ ⏱️ 预计用时: 约15分钟                │ │
  │ │ 执行时间:               │ │ │ 📊 上次合规率: 78%                  │ │
  │ │ ⚫ 立即执行 ⚪ 定时执行   │ │ │                                     │ │
  │ └─────────────────────────┘ │ └─────────────────────────────────────┘ │
  │                                                                         │
  │ 选择目标服务器                                                           │
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │☑️│服务器名称    │IP地址        │状态  │最后检查      │             │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │☑️│Web服务器01   │************* │🟢在线│2024-01-14    │             │ │
  │ │☑️│数据库服务器  │************* │🟢在线│2024-01-14    │             │ │
  │ │☐│应用服务器01  │************* │🔴离线│2024-01-12    │             │ │
  │ │☑️│文件服务器    │************* │🟢在线│2024-01-14    │             │ │
  │ │☑️│备份服务器    │************* │🟢在线│2024-01-13    │             │ │
  │ │☐│测试服务器    │************* │🟡维护│2024-01-10    │  ▲          │ │
  │ │☑️│开发服务器    │192.168.1.106 │🟢在线│2024-01-14    │  █ 滚动条   │ │
  │ │☐│监控服务器    │192.168.1.107 │�在线│2024-01-13    │  ▼          │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  │ 已选择: 5台服务器   ☑️ 全选   ☐ 反选   🔄 刷新状态                     │
  │                                                                         │
  │                    🧪 预览任务    ▶️ 开始执行    ❌ 取消                │
  └─────────────────────────────────────────────────────────────────────────┘
```

## 6. 任务执行进度对话框

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        ▶️ 执行检查任务                                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ 总进度: ████████████████████████████████░░░░░░░░ 80% (4/5服务器完成)             │
│ 当前: 正在检查文件服务器 - SSH配置检查...                                        │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 📝 执行日志                                    🗑️ 清空                          │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │[10:30:01] ✅ Web服务器01 - 用户权限检查通过                                  │ │
│ │[10:30:02] ⚠️ Web服务器01 - 密码策略检查发现问题                              │ │
│ │[10:30:03] ✅ 数据库服务器 - SSH配置检查通过                                  │ │
│ │[10:30:04] ❌ 数据库服务器 - 防火墙配置检查失败                               │ │
│ │[10:30:05] 正在检查文件服务器...                                             │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│           ⏹️ 停止检查    📋 查看结果    ❌ 关闭                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 7. 报告导出页面 (主内容区域固定显示)

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ 📄 报告导出 - 等保合规检查_2024-01-15                                    │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ 总计: 10台服务器  │  已选择: 5台  │  ✅ 正常: 2台  │  ⚠️ 警告: 2台    │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────┐ │ ┌─────────────────────────────────────┐ │
  │ │       导出配置           │ │ │         选择导出服务器               │ │
  │ │                         │ │ │                                     │ │
  │ │ 报告类型:               │ │ │ ┌─────────────────────────────────┐ │ │
  │ │ ⚫ 完整报告              │ │ │ │☑️│服务器名称  │状态  │合规率│问题│ │ │
  │ │ ⚪ 问题报告              │ │ │ ├─────────────────────────────────┤ │ │
  │ │ ⚪ 摘要报告              │ │ │ │☑️│Web服务器01 │✅正常│ 80% │ 3  │ │ │
  │ │                         │ │ │ │☑️│数据库服务器│✅正常│ 87% │ 2  │ │ │
  │ │ 包含内容:               │ │ │ │☐│文件服务器  │⚠️警告│ 67% │ 5  │ │ │
  │ │ ☑️ 检查结果             │ │ │ │☑️│备份服务器  │⚠️警告│ 73% │ 4  │ │ │
  │ │ ☑️ 问题详情             │ │ │ │☐│开发服务器  │❌异常│ 53% │ 7  │ │ │
  │ │ ☑️ 修复建议             │ │ │ │☑️│监控服务器  │✅正常│ 93% │ 1  │ │ │
  │ │ ☐ 执行日志              │ │ │ │☐│日志服务器  │⚠️警告│ 60% │ 6  │ │ │
  │ │                         │ │ │ │☑️│缓存服务器  │✅正常│ 87% │ 2  │▲│ │
  │ │ 导出格式:               │ │ │ │☐│负载均衡器  │⚠️警告│ 67% │ 5  │█│ │
  │ │ ⚫ PDF ⚪ Excel ⚪ Word   │ │ │ │☐│测试服务器  │❌异常│ 47% │ 8  │▼│ │
  │ │                         │ │ │ └─────────────────────────────────┘ │ │
  │ │ 保存位置:               │ │ │ ☑️ 全选  ☐ 反选  🔍 仅异常服务器    │ │
  │ │ [/home/<USER>/______] 📁│ │ │                                     │ │
  │ └─────────────────────────┘ │ └─────────────────────────────────────┘ │
  │                                                                         │
  │              📄 生成报告    🔄 重新检查    ❌ 取消                      │
  └─────────────────────────────────────────────────────────────────────────┘
```

## 功能说明

### 🎯 **4个核心功能模块：**

1. **🖥️ 服务器管理** - 添加、编辑、删除目标服务器
2. **⚙️ 核查配置** - 配置检查项目和规则
3. **▶️ 创建任务** - 选择服务器并执行检查
4. **📄 报告导出** - 选择服务器并导出检查报告

### 📋 **简化的操作流程：**
1. 添加要检查的服务器
2. 配置检查项目和规则
3. 创建检查任务并执行
4. 选择服务器并导出检查报告

### ✨ **报告导出优化：**
- **左侧导出配置** - 选择报告类型、内容、格式
- **右侧服务器选择** - 勾选要导出报告的服务器
- **灵活导出** - 可以选择部分服务器导出报告
- **快捷筛选** - 支持全选、反选、仅异常服务器

界面简洁明了，功能聚焦核心需求！

## 7. 报告导出页面 (主内容区域固定显示)

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ � 报告导出 - 等保合规检查_2024-01-15                                    │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ 总计: 10台服务器  │  已选择: 5台  │  ✅ 正常: 2台  │  ⚠️ 警告: 2台    │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────┐ │ ┌─────────────────────────────────────┐ │
  │ │       导出配置           │ │ │         选择导出服务器               │ │
  │ │                         │ │ │                                     │ │
  │ │ 报告类型:               │ │ │ ┌─────────────────────────────────┐ │ │
  │ │ ⚫ 完整报告              │ │ │ │服务器名称    │状态  │合规率│问题│ │ │
  │ │ ⚪ 问题报告              │ │ │ ├─────────────────────────────────┤ │ │
  │ │ ⚪ 摘要报告              │ │ │ │Web服务器01   │✅正常│ 80% │ 3  │ │ │
  │ │                         │ │ │ │数据库服务器  │✅正常│ 87% │ 2  │ │ │
  │ │ 包含内容:               │ │ │ │文件服务器    │⚠️警告│ 67% │ 5  │ │ │
  │ │ ☑️ 检查结果             │ │ │ │备份服务器    │⚠️警告│ 73% │ 4  │ │ │
  │ │ ☑️ 问题详情             │ │ │ │开发服务器    │❌异常│ 53% │ 7  │ │ │
  │ │ ☑️ 修复建议             │ │ │ │监控服务器    │✅正常│ 93% │ 1  │ │ │
  │ │ ☐ 执行日志              │ │ │ │日志服务器    │⚠️警告│ 60% │ 6  │▲│ │
  │ │                         │ │ │ │缓存服务器    │✅正常│ 87% │ 2  │█│ │
  │ │ 导出格式:               │ │ │ │负载均衡器    │⚠️警告│ 67% │ 5  │▼│ │
  │ │ ⚫ PDF ⚪ Excel ⚪ Word   │ │ │ └─────────────────────────────────┘ │ │
  │ │                         │ │ │                                     │ │
  │ │ 保存位置:               │ │ │                                     │ │
  │ │ [/home/<USER>/______] 📁│ │ │                                     │ │
  │ └─────────────────────────┘ │ └─────────────────────────────────────┘ │
  │                                                                         │
  │              📄 生成报告    🔄 重新检查    ❌ 取消                      │
  └─────────────────────────────────────────────────────────────────────────┘
```

## 功能说明

### 🎯 **4个核心功能模块：**

1. **🖥️ 服务器管理** - 添加、编辑、删除目标服务器
2. **⚙️ 核查配置** - 配置检查项目和规则
3. **▶️ 创建任务** - 选择服务器并执行检查
4. **📄 报告导出** - 查看结果并配置导出报告

### 📋 **简化的操作流程：**
1. 添加要检查的服务器
2. 配置检查项目和规则
3. 创建检查任务并执行
4. 配置并导出检查报告

### ✨ **合并优化：**
- **检查结果 + 报告导出** 合并为一个页面
- **左侧导出配置** - 选择报告类型、内容、格式
- **右侧结果预览** - 实时显示检查结果
- **一站式操作** - 查看结果的同时配置导出

界面简洁明了，功能聚焦核心需求！
