# Linux等保合规检查工具 - UI界面设计图

## 1. 主界面布局 (固定大小: 1200x800px)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                                    👤 admin ⚙️ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🖥️ 服务器管理 │ ⚙️ 核查配置 │ ▶️ 创建任务 │ 📄 报告导出                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域 (固定显示)                                   │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                                                                         │   │
│  │                        当前选中功能的内容                                │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 📦 版本: v1.0.0                                          │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 服务器管理页面 (在主内容区域显示，支持滚动)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🖥️ 服务器管理                                                                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│        ➕ 添加服务器    🔄 刷新    🗑️ 删除                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │☑️│服务器名称    │IP地址        │端口│状态  │操作        │                   │ │
│ ├─────────────────────────────────────────────────────────────────────────────┤ │
│ │☑️│Web服务器01   │************* │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│数据库服务器  │************* │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│应用服务器01  │************* │22  │🔴离线│编辑|删除|测试│                   │ │
│ │☑️│文件服务器    │************* │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│备份服务器    │************* │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│测试服务器    │192.168.1.105 │22  │🟡维护│编辑|删除|测试│                   │ │
│ │☑️│开发服务器    │192.168.1.106 │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│监控服务器    │192.168.1.107 │22  │🟢在线│编辑|删除|测试│                   │ │
│ │☑️│日志服务器    │192.168.1.108 │22  │🟢在线│编辑|删除|测试│  ▲               │ │
│ │☑️│缓存服务器    │192.168.1.109 │22  │🟢在线│编辑|删除|测试│  █ 滚动条        │ │
│ │☑️│负载均衡器    │192.168.1.110 │22  │🟢在线│编辑|删除|测试│  ▼               │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 添加服务器对话框

```
                    ┌─────────────────────────────────────┐
                    │        📝 添加服务器                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 服务器名称: [Web服务器01_________]   │
                    │ IP地址:    [*************_______]   │
                    │ SSH端口:   [22]                     │
                    │ 用户名:    [root_______________]    │
                    │                                     │
                    │ 认证方式:                           │
                    │ ⚫ 密码认证  ⚪ 密钥认证             │
                    │                                     │
                    │ 密码:      [******************]    │
                    │ 密钥文件:  [选择文件...] 📁         │
                    │                                     │
                    │  🔍 测试连接   ❌ 取消   ✅ 确定   │
                    └─────────────────────────────────────┘
```

## 4. 核查配置页面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ⚙️ 核查配置                                                                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐ │ ┌─────────────────────────────────────────────────┐ │
│ │    检查分类          │ │ │            检查项详情                            │ │
│ │                     │ │ │                                                 │ │
│ │ 📁 系统安全          │ │ │ 检查项名称: [用户权限检查_______________]        │ │
│ │ ├─ ☑️ 用户权限检查   │ │ │                                                 │ │
│ │ ├─ ☑️ 密码策略检查   │ │ │ 严重级别: [高危 ▼]                              │ │
│ │ └─ ☐ 系统配置检查   │ │ │                                                 │ │
│ │                     │ │ │ 检查命令:                                       │ │
│ │ 📁 网络安全          │ │ │ ┌─────────────────────────────────────────────┐ │ │
│ │ ├─ ☑️ 防火墙配置     │ │ │ │ cat /etc/passwd | grep root                 │ │ │
│ │ └─ ☐ 端口扫描       │ │ │ │ ls -la /etc/shadow                          │ │ │
│ │                     │ │ │ └─────────────────────────────────────────────┘ │ │
│ │ 📁 应用安全          │ │ │                                                 │ │
│ │ └─ ☑️ 服务配置检查   │ │ │ 修复建议:                                       │ │
│ │                     │ │ │ ┌─────────────────────────────────────────────┐ │ │
│ │                     │ │ │ │ 检查root用户配置是否正确                     │ │ │
│ │                     │ │ │ └─────────────────────────────────────────────┘ │ │
│ └─────────────────────┘ │ └─────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                    💾 保存配置    ❌ 取消                                        │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 5. 创建任务页面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ▶️ 创建检查任务                                                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ ┌─────────────────────────────────────┐ ┌─────────────────────────────────────┐ │
│ │           任务信息                   │ │         检查配置预览                 │ │
│ │                                     │ │                                     │ │
│ │ 任务名称:                           │ │ 📋 已配置检查项 (共15项)             │ │
│ │ [等保合规检查_2024-01-15_________]   │ │                                     │ │
│ │                                     │ │ 🔴 高危检查: 5项                    │ │
│ │ 任务描述:                           │ │ 🟡 中危检查: 7项                    │ │
│ │ [定期安全合规检查_______________]    │ │ 🟢 低危检查: 3项                    │ │
│ │ [_________________________________] │ │                                     │ │
│ │                                     │ │ ⏱️ 预计用时: 约15分钟                │ │
│ │ 执行时间:                           │ │                                     │ │
│ │ ⚫ 立即执行  ⚪ 定时执行             │ │ 📊 上次合规率: 78%                  │ │
│ │                                     │ │                                     │ │
│ └─────────────────────────────────────┘ └─────────────────────────────────────┘ │
│                                                                                 │
│ 选择目标服务器                                                                   │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                                                                             │ │
│ │  ☑️ Web服务器01      ☑️ 数据库服务器    ☐ 应用服务器01    ☑️ 文件服务器      │ │
│ │  (*************)    (*************)   (*************)   (*************)   │ │
│ │  🟢 在线             🟢 在线            🔴 离线            🟢 在线             │ │
│ │                                                                             │ │
│ │  ☑️ 备份服务器       ☐ 测试服务器       ☑️ 开发服务器     ☐ 监控服务器       │ │
│ │  (*************)    (192.168.1.105)   (192.168.1.106)   (192.168.1.107)   │ │
│ │  🟢 在线             🟡 维护            🟢 在线            🟢 在线             │ │
│ │                                                                             │ │
│ │  已选择: 5台服务器   ☑️ 全选   ☐ 反选   🔄 刷新状态                         │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│                    🧪 预览任务    ▶️ 开始执行    ❌ 取消                        │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 6. 任务执行进度对话框

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        ▶️ 执行检查任务                                          │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ 总进度: ████████████████████████████████░░░░░░░░ 80% (4/5服务器完成)             │
│ 当前: 正在检查文件服务器 - SSH配置检查...                                        │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 📝 执行日志                                    🗑️ 清空                          │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │[10:30:01] ✅ Web服务器01 - 用户权限检查通过                                  │ │
│ │[10:30:02] ⚠️ Web服务器01 - 密码策略检查发现问题                              │ │
│ │[10:30:03] ✅ 数据库服务器 - SSH配置检查通过                                  │ │
│ │[10:30:04] ❌ 数据库服务器 - 防火墙配置检查失败                               │ │
│ │[10:30:05] 正在检查文件服务器...                                             │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│           ⏹️ 停止检查    📋 查看结果    ❌ 关闭                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 7. 检查结果页面 (在主内容区域显示，支持滚动)

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📋 检查结果 - 等保合规检查_2024-01-15                                            │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ✅ 通过: 15项  │  ⚠️ 警告: 3项  │  ❌ 失败: 2项  │  📊 合规率: 75%              │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │检查项目        │服务器        │级别│状态  │问题描述          │操作    │     │ │
│ ├─────────────────────────────────────────────────────────────────────────────┤ │
│ │用户权限检查    │Web服务器01   │高危│✅通过│无问题            │查看    │     │ │
│ │密码策略检查    │Web服务器01   │中危│⚠️警告│密码复杂度不足     │查看    │     │ │
│ │SSH配置检查     │数据库服务器  │高危│✅通过│无问题            │查看    │     │ │
│ │防火墙配置      │数据库服务器  │中危│❌失败│端口配置错误       │查看    │     │ │
│ │服务配置检查    │文件服务器    │中危│✅通过│无问题            │查看    │     │ │
│ │系统日志检查    │Web服务器01   │低危│✅通过│无问题            │查看    │     │ │
│ │端口扫描检查    │应用服务器01  │中危│⚠️警告│开放端口过多       │查看    │     │ │
│ │文件权限检查    │文件服务器    │高危│❌失败│权限配置错误       │查看    │  ▲  │ │
│ │进程检查        │数据库服务器  │低危│✅通过│无问题            │查看    │  █  │ │
│ │网络配置检查    │负载均衡器    │中危│✅通过│无问题            │查看    │  ▼  │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│        📄 生成报告    🔄 重新检查    📤 导出结果                                │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 8. 报告导出对话框

```
                    ┌─────────────────────────────────────┐
                    │        📄 导出检查报告              │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 报告类型:                           │
                    │ ⚫ 完整报告  ⚪ 问题报告  ⚪ 摘要报告 │
                    │                                     │
                    │ 包含内容:                           │
                    │ ☑️ 检查结果  ☑️ 问题详情            │
                    │ ☑️ 修复建议  ☐ 执行日志             │
                    │                                     │
                    │ 导出格式:                           │
                    │ ⚫ PDF  ⚪ Excel  ⚪ Word            │
                    │                                     │
                    │ 保存位置:                           │
                    │ [/home/<USER>/____________] 📁     │
                    │                                     │
                    │  📄 生成报告   ❌ 取消              │
                    └─────────────────────────────────────┘
```

## 功能说明

### 🎯 **4个核心功能模块：**

1. **🖥️ 服务器管理** - 添加、编辑、删除目标服务器
2. **⚙️ 核查配置** - 配置检查项目和规则  
3. **▶️ 创建任务** - 选择服务器和检查项，执行检查
4. **📄 报告导出** - 查看结果，生成和导出报告

### 📋 **简化的操作流程：**
1. 添加要检查的服务器
2. 配置检查项目和规则
3. 创建检查任务并执行
4. 查看结果并导出报告

界面简洁明了，功能聚焦核心需求！
