# Linux等保合规检查工具 UI界面设计图

## 1. 主界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                           🔔 👤 admin ⚙️ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 📊 仪表板 │ 🖥️ 服务器管理 │ ⚙️ 检查配置 │ ▶️ 执行检查 │ 📋 结果查看 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域                                              │
│                     (页面内容显示区域)                                           │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 👤 当前用户: admin │ 📦 版本: v1.0.0                    │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 仪表板页面

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 📊 仪表板                                                                        │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                │
│ │   🖥️        │ │    ✅       │ │    ⚠️       │ │    🛡️       │                │
│ │ 服务器总数   │ │  检查任务   │ │  风险项目   │ │   合规率    │                │
│ │     12      │ │      8      │ │      3      │ │    85%     │                │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────┐ ┌─────────────────────────────┐                │
│ │        快速操作              │ │        最近活动              │                │
│ │                             │ │                             │                │
│ │ ➕ 添加服务器                │ │ 🔍 检查任务完成 - 2分钟前    │                │
│ │ ▶️ 执行检查                  │ │ ➕ 新增服务器 - 10分钟前     │                │
│ │ 📄 生成报告                  │ │ 📄 报告生成 - 1小时前        │                │
│ │                             │ │ ⚠️ 发现风险项 - 2小时前      │                │
│ └─────────────────────────────┘ └─────────────────────────────┘                │
│                                                                                 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │                          📈 检查结果趋势图                                   │ │
│ │                                                                             │ │
│ │     ▲                                                                       │ │
│ │  100│     ●                                                                 │ │
│ │   80│   ●   ●                                                               │ │
│ │   60│ ●       ●                                                             │ │
│ │   40│           ●                                                           │ │
│ │   20│             ●                                                         │ │
│ │    0└─────────────────────────────────────────────────────────────────────▶ │ │
│ │     1月  2月  3月  4月  5月  6月                                            │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 服务器连接配置对话框

```
                    ┌─────────────────────────────────────┐
                    │        📝 添加服务器                │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 基本信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 服务器名称: [Web服务器01_______] │ │
                    │ │ IP地址/域名: [*************____] │ │
                    │ │ SSH端口: [22] 系统版本: [CentOS] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 认证信息                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 认证方式: ⚫ 密码认证 ⚪ 密钥认证 │ │
                    │ │ 用户名: [root_______________]   │ │
                    │ │ 密码: [******************]     │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │ 高级设置                            │
                    │ ┌─────────────────────────────────┐ │
                    │ │ 连接超时: [30秒] Sudo密码: [可选] │ │
                    │ │ 描述: [________________________] │ │
                    │ │      [________________________] │ │
                    │ └─────────────────────────────────┘ │
                    │                                     │
                    │  🔍 测试连接   ❌ 取消   ✅ 确定   │
                    └─────────────────────────────────────┘
```

## 4. 检查项配置对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           ⚙️ 检查项配置                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐ │ ┌─────────────────────────────────────────────┐ │
│ │    检查分类树        │ │ │            检查项详情配置                    │ │
│ │                     │ │ │                                             │ │
│ │ 📁 系统安全          │ │ │ 检查项名称: [用户权限检查_______________]    │ │
│ │ ├─ ☑️ 用户权限检查   │ │ │                                             │ │
│ │ ├─ ☑️ 密码策略检查   │ │ │ 严重级别: [高危 ▼]                          │ │
│ │ └─ ☐ 系统配置检查   │ │ │                                             │ │
│ │                     │ │ │ 检查命令:                                   │ │
│ │ 📁 网络安全          │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │ ├─ ☑️ 防火墙配置     │ │ │ │ cat /etc/passwd | grep root             │ │ │
│ │ └─ ☐ 端口扫描       │ │ │ │ ls -la /etc/shadow                      │ │ │
│ │                     │ │ │ └─────────────────────────────────────────┘ │ │
│ │ 📁 应用安全          │ │ │                                             │ │
│ │ └─ ☑️ 服务配置检查   │ │ │ 期望结果: [root:x:0:0:root:/root:/bin/bash] │ │
│ │                     │ │ │                                             │ │
│ │                     │ │ │ 修复建议:                                   │ │
│ │                     │ │ │ ┌─────────────────────────────────────────┐ │ │
│ │                     │ │ │ │ 检查root用户配置是否正确，确保权限设置   │ │ │
│ │                     │ │ │ │ 符合等保要求                            │ │ │
│ │                     │ │ │ └─────────────────────────────────────────┘ │ │
│ └─────────────────────┘ │ └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│        📋 导入模板    💾 保存配置    ❌ 取消    ✅ 确定                      │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. 执行检查进度对话框

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        ▶️ 执行检查进度                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ 总检查项: 45    已完成: 32    发现问题: 5                                    │
│ ████████████████████████████████░░░░░░░░ 71%                                │
│ 当前任务: 正在检查SSH配置安全性...                                           │
│                                                                             │
├─────────────────────────────────────────────────────────────────────────────┤
│ 📝 执行日志                                    🗑️ 清空日志                  │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │[2024-01-15 10:30:01] 开始检查用户权限...                                │ │
│ │[2024-01-15 10:30:02] ✅ 用户权限检查通过                                │ │
│ │[2024-01-15 10:30:03] 开始检查密码策略...                                │ │
│ │[2024-01-15 10:30:04] ⚠️ 发现密码策略问题                                │ │
│ │[2024-01-15 10:30:05] 开始检查SSH配置...                                 │ │
│ │[2024-01-15 10:30:06] 正在验证SSH端口配置...                             │ │
│ │[2024-01-15 10:30:07] 检查防火墙规则...                                  │ │
│ │[2024-01-15 10:30:08] ✅ 防火墙配置正常                                  │ │
│ │[滚动显示更多日志内容...]                                                │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│           ⏸️ 暂停检查    ⏹️ 停止检查    ❌ 关闭                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. 检查结果展示页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 📋 检查结果                                                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│ ✅ 通过项目: 42  │  ⚠️ 警告项目: 8  │  ❌ 失败项目: 3  │  📊 总体评分: 85分 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 服务器: [全部 ▼] │ 严重级别: [全部 ▼] │ 状态: [全部 ▼] │ 🔍 [搜索关键词___] │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │检查项目        │服务器        │级别│状态  │检查时间  │操作      │         │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │用户权限检查    │Web服务器01   │高危│❌失败│10:30:01 │查看详情  │         │ │
│ │SSH配置检查     │Web服务器01   │中危│⚠️警告│10:30:02 │查看详情  │         │ │
│ │防火墙配置      │数据库服务器  │高危│✅通过│10:30:03 │查看详情  │         │ │
│ │密码策略检查    │应用服务器01  │中危│⚠️警告│10:30:04 │查看详情  │         │ │
│ │服务配置检查    │文件服务器    │低危│✅通过│10:30:05 │查看详情  │         │ │
│ │端口扫描检查    │Web服务器01   │中危│❌失败│10:30:06 │查看详情  │         │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│        📄 生成报告    📤 导出结果    🔄 重新检查    📧 发送邮件              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. 服务器管理页面

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🖥️ 服务器管理                                                               │
│ 管理需要进行等保合规检查的Linux服务器                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│        🔄 刷新    ➕ 添加服务器    🗑️ 批量删除    📤 导出列表               │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │☑️│服务器名称    │IP地址        │端口│状态  │最后检查  │操作        │     │ │
│ ├─────────────────────────────────────────────────────────────────────────┤ │
│ │☑️│Web服务器01   │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│数据库服务器  │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│应用服务器01  │************* │22  │🔴离线│2024-01-14│编辑|删除|测试│     │ │
│ │☑️│文件服务器    │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ │☑️│备份服务器    │************* │22  │🟢在线│2024-01-15│编辑|删除|测试│     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ 共 25 条记录 │ 每页显示: [10▼] │ ◀️ 1 2 3 4 5 ▶️                           │
└─────────────────────────────────────────────────────────────────────────────┘
```

现在这样用ASCII字符画出的界面框架，你应该能清楚地看到每个界面的具体布局和样子了！每个界面都有明确的边框、区域划分和功能元素。
